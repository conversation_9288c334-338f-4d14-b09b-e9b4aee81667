<template>
  <div class="container mt-5">
    <h2>Notification Filter Demo</h2>
    <p>This page demonstrates the notification filter functionality.</p>
    
    <div class="row">
      <div class="col-md-6">
        <h4>Filter Controls</h4>
        <div class="card p-3">
          <div class="form-group">
            <label>
              <input type="checkbox" v-model="filters.CreatedBid" @change="updateFilters"> 
              Bid Created
            </label>
          </div>
          <div class="form-group">
            <label>
              <input type="checkbox" v-model="filters.BidHasBeenUpdated" @change="updateFilters"> 
              Bid Updated
            </label>
          </div>
          <div class="form-group">
            <label>
              <input type="checkbox" v-model="filters.InvitedToBid" @change="updateFilters"> 
              Invited to Bid
            </label>
          </div>
          <div class="form-group">
            <label>
              <input type="checkbox" v-model="filters.SubmittedOffer" @change="updateFilters"> 
              Offer Submitted
            </label>
          </div>
        </div>
      </div>
      
      <div class="col-md-6">
        <h4>Sample Notifications</h4>
        <div class="notifications-list">
          <div 
            v-for="notification in filteredNotifications" 
            :key="notification.id"
            class="notification-item p-3 mb-2 border rounded"
            :class="{ 'unread': !notification.readed }"
          >
            <div class="d-flex">
              <div class="icon-wrapper me-3">
                <svg class="icon">
                  <use xlink:href="~/static/sprite.svg#act-create" v-if="notification.type === 'CreatedBid' || notification.type === 'BidHasBeenUpdated'"></use>
                  <use xlink:href="~/static/sprite.svg#act-invite" v-if="notification.type === 'InvitedToBid'"></use>
                  <use xlink:href="~/static/sprite.svg#act-submit" v-if="notification.type === 'SubmittedOffer'"></use>
                </svg>
              </div>
              <div class="notification-content">
                <h6>{{ notification.text }}</h6>
                <small class="text-muted">{{ notification.type }}</small>
                <p class="mb-0 text-muted">{{ formatDate(notification.created_at) }}</p>
              </div>
            </div>
          </div>
          
          <div v-if="filteredNotifications.length === 0" class="text-center text-muted p-4">
            No notifications match the current filters
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TestNotifications',
  data() {
    return {
      filters: {
        CreatedBid: true,
        BidHasBeenUpdated: true,
        InvitedToBid: true,
        SubmittedOffer: true,
      },
      sampleNotifications: [
        {
          id: 1,
          type: 'CreatedBid',
          text: 'New bid has been created: Construction Project',
          created_at: new Date().toISOString(),
          readed: false
        },
        {
          id: 2,
          type: 'BidHasBeenUpdated',
          text: 'Bid has been updated: IT Services Contract',
          created_at: new Date(Date.now() - 3600000).toISOString(),
          readed: true
        },
        {
          id: 3,
          type: 'InvitedToBid',
          text: 'You have been invited to bid on: Marketing Campaign',
          created_at: new Date(Date.now() - 7200000).toISOString(),
          readed: false
        },
        {
          id: 4,
          type: 'SubmittedOffer',
          text: 'New offer submitted for: Office Supplies',
          created_at: new Date(Date.now() - 10800000).toISOString(),
          readed: true
        }
      ]
    }
  },
  computed: {
    filteredNotifications() {
      return this.sampleNotifications.filter(notification => 
        this.filters[notification.type] !== false
      )
    }
  },
  methods: {
    updateFilters() {
      console.log('Filters updated:', this.filters)
    },
    formatDate(dateString) {
      return new Date(dateString).toLocaleString()
    }
  }
}
</script>

<style scoped>
.notification-item {
  background-color: #f8f9fa;
  transition: all 0.2s ease;
}

.notification-item.unread {
  background-color: #ebfaf5;
  border-left: 4px solid #1e805d;
}

.notification-item:hover {
  background-color: #e9ecef;
}

.icon-wrapper {
  width: 40px;
  height: 40px;
  border-radius: 6px;
  background-color: #ebfaf5;
  display: flex;
  justify-content: center;
  align-items: center;
}

.icon {
  width: 20px;
  height: 20px;
  stroke: #1e805d;
}

.form-group {
  margin-bottom: 10px;
}

.form-group label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-weight: 500;
}

.form-group input[type="checkbox"] {
  margin-right: 8px;
  accent-color: #1e805d;
}
</style>
